.PHONY: build package test clean install deps dev fmt lint bundle help release init check quick-build info

# 变量定义
APP_NAME := linux-security-checker
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "v0.1.0")
BUILD_DIR := dist
ICON_PATH := assets/icons/app.png

# 构建标志
LDFLAGS := -X main.Version=$(VERSION) -X main.BuildTime=$(shell date -u '+%Y-%m-%d_%H:%M:%S') -X main.GoVersion=$(shell go version | awk '{print $$3}')

# 默认目标 - 只构建Windows版本
all: deps build

# 显示帮助信息
help:
	@echo "Linux等保基线核查工具 - 构建脚本"
	@echo "只构建Windows版本，符合项目需求"
	@echo ""
	@echo "可用的命令:"
	@echo "  deps        - 安装依赖"
	@echo "  build       - 构建Windows版本"
	@echo "  package     - 使用Fyne打包Windows版本"
	@echo "  test        - 运行测试并构建Windows版本"
	@echo "  clean       - 清理构建文件"
	@echo "  install     - 安装到本地"
	@echo "  dev         - 开发模式运行"
	@echo "  fmt         - 格式化代码"
	@echo "  lint        - 代码检查"
	@echo "  bundle      - 生成资源文件"
	@echo "  release     - 创建Windows发布版本"
	@echo "  init        - 初始化项目结构"
	@echo "  check       - 运行所有检查"
	@echo "  quick-build - 快速构建Windows版本(跳过测试)"
	@echo "  info        - 显示构建信息"

# 安装依赖
deps:
	@echo "安装Go依赖..."
	go mod download
	go mod tidy
	@echo "安装Fyne工具..."
	go install fyne.io/tools/cmd/fyne@latest
	@echo "✅ 依赖安装完成!"

# 构建Windows版本 (唯一目标)
build:
	@echo "构建 $(APP_NAME) $(VERSION) Windows版本..."
	@mkdir -p $(BUILD_DIR)
	@echo "正在构建Windows x64可执行文件..."
	@echo "使用交叉编译: GOOS=windows GOARCH=amd64"
	@GOOS=windows GOARCH=amd64 CGO_ENABLED=1 CC=x86_64-w64-mingw32-gcc go build -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe cmd/main.go
	@echo "✅ Windows版本构建完成: $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe"
	@echo "📁 可执行文件位置: $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe"

# Fyne打包Windows版本
package: build
	@echo "使用Fyne打包Windows版本..."
	@if [ ! -f $(ICON_PATH) ]; then \
		echo "⚠️  警告: 图标文件 $(ICON_PATH) 不存在，创建默认图标"; \
		mkdir -p assets/icons; \
		touch $(ICON_PATH); \
	fi
	@echo "正在使用Fyne打包Windows可执行文件..."
	@fyne package -os windows -icon $(ICON_PATH) -name $(APP_NAME) cmd/main.go
	@mv $(APP_NAME).exe $(BUILD_DIR)/ 2>/dev/null || true
	@echo "✅ Windows版本打包完成: $(BUILD_DIR)/$(APP_NAME).exe"

# 运行测试并构建Windows版本
test: build
	@echo "运行测试..."
	go test -v ./...
	@echo "✅ 测试完成!"
	@echo "✅ Windows版本已构建，可供下载测试"

# 测试覆盖率并构建Windows版本
test-coverage: build
	@echo "运行测试并生成覆盖率报告..."
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "✅ 覆盖率报告生成完成: coverage.html"
	@echo "✅ Windows版本已构建，可供下载测试"

# 清理构建文件
clean:
	@echo "清理构建文件..."
	rm -rf $(BUILD_DIR)
	rm -f $(APP_NAME).exe
	rm -f coverage.out coverage.html
	@echo "✅ 清理完成!"

# 安装到本地 (仅用于开发)
install:
	@echo "安装到本地 (仅用于开发调试)..."
	go install -ldflags="$(LDFLAGS)" cmd/main.go
	@echo "✅ 安装完成!"
	@echo "注意: 生产环境请使用Windows版本"

# 开发模式运行 (仅用于开发调试)
dev:
	@echo "开发模式运行 (仅用于开发调试)..."
	@echo "注意: 生产环境请使用Windows版本"
	go run cmd/main.go



# 格式化代码
fmt:
	@echo "格式化代码..."
	go fmt ./...
	@echo "代码格式化完成!"

# 代码检查
lint:
	@echo "代码检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint 未安装，跳过代码检查"; \
		echo "安装命令: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

# 生成资源文件
bundle:
	@echo "生成资源文件..."
	@mkdir -p internal/gui/resources
	@if [ -f $(ICON_PATH) ]; then \
		fyne bundle -o internal/gui/resources/icon.go $(ICON_PATH); \
		echo "图标资源文件生成完成"; \
	else \
		echo "图标文件不存在，跳过资源生成"; \
	fi

# 创建Windows发布版本
release: clean deps test build package
	@echo "创建Windows发布版本 $(VERSION)..."
	@mkdir -p $(BUILD_DIR)/release
	@cp $(BUILD_DIR)/$(APP_NAME).exe $(BUILD_DIR)/release/ 2>/dev/null || cp $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe $(BUILD_DIR)/release/$(APP_NAME).exe
	@cp README.md $(BUILD_DIR)/release/ 2>/dev/null || echo "README.md not found, skipping..."
	@cp docs/用户手册.md $(BUILD_DIR)/release/ 2>/dev/null || echo "用户手册.md not found, skipping..."
	@echo "正在创建Windows发布包..."
	@cd $(BUILD_DIR) && zip -r $(APP_NAME)-$(VERSION)-windows-amd64.zip release/
	@echo "Windows发布版本创建完成: $(BUILD_DIR)/$(APP_NAME)-$(VERSION)-windows-amd64.zip"

# 初始化项目结构
init:
	@echo "初始化项目结构..."
	@mkdir -p cmd internal/gui/pages internal/gui/components internal/gui/themes
	@mkdir -p internal/core/host internal/core/rule internal/core/checker internal/core/export
	@mkdir -p internal/db/models internal/db/migrations internal/config
	@mkdir -p configs rules/system rules/network rules/audit rules/filesystem
	@mkdir -p assets/icons assets/images assets/fonts
	@mkdir -p docs scripts tests
	@echo "项目结构初始化完成!"

# 运行所有检查
check: fmt lint test
	@echo "✅ 所有检查完成!"

# 快速构建Windows版本 (跳过测试)
quick-build: deps build
	@echo "✅ Windows版本快速构建完成!"
	@echo "📁 可执行文件位置: $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe"

# 显示构建信息
info:
	@echo "项目信息:"
	@echo "  名称: $(APP_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  目标平台: Windows x64"
	@echo "  构建目录: $(BUILD_DIR)"
	@echo "  Go版本: $(shell go version)"
	@echo "  Fyne版本: $(shell fyne version 2>/dev/null || echo '未安装')"
