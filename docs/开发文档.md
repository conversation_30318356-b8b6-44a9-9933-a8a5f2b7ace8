# Linux等保基线核查工具开发文档

## 1. 开发环境搭建

### 1.1 系统要求
- **操作系统**: Windows 10/11, Linux, macOS
- **Go版本**: Go 1.21 或更高版本
- **内存**: 最少 4GB RAM
- **存储**: 最少 2GB 可用空间

### 1.2 Go环境安装

#### Windows (推荐使用MSYS2)
```bash
# 安装MSYS2后，在MSYS2终端中执行
pacman -Syu
pacman -S git mingw-w64-x86_64-toolchain mingw-w64-x86_64-go
echo "export PATH=\$PATH:~/Go/bin" >> ~/.bashrc
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get install golang gcc libgl1-mesa-dev xorg-dev libxkbcommon-dev
```

#### macOS
```bash
# 安装Xcode命令行工具
xcode-select --install
```

### 1.3 验证Go安装
```bash
go version
# 应该显示: go version go1.21.x
```

## 2. 项目初始化

### 2.1 创建项目目录
```bash
mkdir linux-security-checker
cd linux-security-checker
go mod init github.com/yourname/linux-security-checker
```

### 2.2 安装核心依赖
```bash
# 安装Fyne GUI框架
go get fyne.io/fyne/v2@latest

# 安装Fyne命令行工具
go install fyne.io/tools/cmd/fyne@latest

# 安装SSH库
go get golang.org/x/crypto/ssh

# 安装SQLite驱动
go get github.com/mattn/go-sqlite3

# 安装其他依赖
go get gopkg.in/yaml.v3
go get github.com/sirupsen/logrus
```

### 2.3 验证Fyne安装
```bash
# 测试Fyne工具
fyne version

# 运行Fyne演示
go install fyne.io/demo@latest
demo
```

## 3. 项目结构设计

### 3.1 目录结构
```
linux-security-checker/
├── cmd/
│   └── main.go                 # 主程序入口
├── internal/
│   ├── gui/                    # GUI相关代码
│   │   ├── app.go             # 应用主框架
│   │   ├── pages/             # 各页面实现
│   │   │   ├── hosts.go       # 主机管理页面
│   │   │   ├── rules.go       # 规则管理页面
│   │   │   ├── tasks.go       # 核查任务页面
│   │   │   ├── results.go     # 结果查看页面
│   │   │   └── settings.go    # 系统设置页面
│   │   ├── components/        # 通用组件
│   │   │   ├── sidebar.go     # 侧边栏组件
│   │   │   ├── toolbar.go     # 工具栏组件
│   │   │   └── dialogs.go     # 对话框组件
│   │   └── themes/            # 主题定义
│   ├── core/                  # 核心业务逻辑
│   │   ├── host/              # 主机管理
│   │   │   ├── manager.go     # 主机管理器
│   │   │   ├── group.go       # 主机组管理
│   │   │   └── ssh.go         # SSH连接管理
│   │   ├── rule/              # 规则管理
│   │   │   ├── engine.go      # 规则引擎
│   │   │   ├── parser.go      # 规则解析器
│   │   │   └── validator.go   # 规则验证器
│   │   ├── checker/           # 核查引擎
│   │   │   ├── executor.go    # 核查执行器
│   │   │   ├── scheduler.go   # 任务调度器
│   │   │   └── collector.go   # 结果收集器
│   │   └── export/            # 导出功能
│   │       ├── pdf.go         # PDF导出
│   │       ├── excel.go       # Excel导出
│   │       └── html.go        # HTML导出
│   ├── db/                    # 数据库操作
│   │   ├── models/            # 数据模型
│   │   │   ├── host.go        # 主机模型
│   │   │   ├── rule.go        # 规则模型
│   │   │   ├── task.go        # 任务模型
│   │   │   └── result.go      # 结果模型
│   │   ├── migrations/        # 数据库迁移
│   │   │   └── init.sql       # 初始化脚本
│   │   └── database.go        # 数据库连接
│   └── config/                # 配置管理
│       ├── config.go          # 配置结构
│       └── loader.go          # 配置加载器
├── configs/                   # 配置文件
│   ├── app.yaml              # 应用配置
│   └── database.yaml         # 数据库配置
├── rules/                     # 等保基线规则
│   ├── system/               # 系统安全规则
│   ├── network/              # 网络安全规则
│   ├── audit/                # 审计规则
│   └── filesystem/           # 文件系统规则
├── assets/                    # 静态资源
│   ├── icons/                # 图标文件
│   ├── images/               # 图片资源
│   └── fonts/                # 字体文件
├── docs/                      # 文档
├── scripts/                   # 构建脚本
│   ├── build.sh              # 构建脚本
│   ├── package.sh            # 打包脚本
│   └── cross-compile.sh      # 交叉编译脚本
├── tests/                     # 测试文件
├── go.mod                     # Go模块文件
├── go.sum                     # 依赖校验文件
├── Makefile                   # 构建配置
└── README.md                  # 项目说明
```

## 4. 核心技术实现

### 4.1 Fyne GUI框架基础

#### 4.1.1 基本应用结构
```go
package main

import (
    "fyne.io/fyne/v2/app"
    "fyne.io/fyne/v2/container"
    "fyne.io/fyne/v2/widget"
)

func main() {
    myApp := app.New()
    myWindow := myApp.NewWindow("Linux等保基线核查工具")
    
    // 设置窗口大小
    myWindow.Resize(fyne.NewSize(1200, 800))
    
    // 创建侧边栏布局
    content := createMainLayout()
    myWindow.SetContent(content)
    
    myWindow.ShowAndRun()
}
```

#### 4.1.2 侧边栏导航实现
```go
func createMainLayout() *container.Split {
    // 创建侧边栏
    sidebar := createSidebar()
    
    // 创建主内容区
    mainContent := container.NewStack()
    
    // 创建分割布局
    split := container.NewHSplit(sidebar, mainContent)
    split.SetOffset(0.2) // 侧边栏占20%宽度
    
    return split
}

func createSidebar() *container.VBox {
    return container.NewVBox(
        widget.NewButton("主机管理", func() {
            // 切换到主机管理页面
        }),
        widget.NewButton("核查规则", func() {
            // 切换到规则管理页面
        }),
        widget.NewButton("核查任务", func() {
            // 切换到任务页面
        }),
        widget.NewButton("核查结果", func() {
            // 切换到结果页面
        }),
        widget.NewButton("系统设置", func() {
            // 切换到设置页面
        }),
    )
}
```

### 4.2 SQLite数据库设计

#### 4.2.1 数据库初始化
```go
package db

import (
    "database/sql"
    _ "github.com/mattn/go-sqlite3"
)

type Database struct {
    conn *sql.DB
}

func NewDatabase(dbPath string) (*Database, error) {
    conn, err := sql.Open("sqlite3", dbPath)
    if err != nil {
        return nil, err
    }
    
    db := &Database{conn: conn}
    if err := db.migrate(); err != nil {
        return nil, err
    }
    
    return db, nil
}

func (db *Database) migrate() error {
    queries := []string{
        `CREATE TABLE IF NOT EXISTS hosts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            ip_address TEXT NOT NULL,
            port INTEGER DEFAULT 22,
            username TEXT NOT NULL,
            auth_type TEXT NOT NULL,
            password TEXT,
            private_key TEXT,
            group_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        `CREATE TABLE IF NOT EXISTS host_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        `CREATE TABLE IF NOT EXISTS rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            category TEXT NOT NULL,
            description TEXT,
            command TEXT NOT NULL,
            expected_result TEXT,
            severity TEXT DEFAULT 'medium',
            enabled BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        `CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL, -- 'single' or 'group'
            target_id INTEGER NOT NULL,
            rule_ids TEXT, -- JSON array of rule IDs
            status TEXT DEFAULT 'pending',
            progress INTEGER DEFAULT 0,
            started_at DATETIME,
            completed_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        `CREATE TABLE IF NOT EXISTS results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id INTEGER NOT NULL,
            host_id INTEGER NOT NULL,
            rule_id INTEGER NOT NULL,
            status TEXT NOT NULL, -- 'pass', 'fail', 'error'
            output TEXT,
            error_message TEXT,
            checked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (task_id) REFERENCES tasks(id),
            FOREIGN KEY (host_id) REFERENCES hosts(id),
            FOREIGN KEY (rule_id) REFERENCES rules(id)
        )`,
    }
    
    for _, query := range queries {
        if _, err := db.conn.Exec(query); err != nil {
            return err
        }
    }
    
    return nil
}
```

### 4.3 SSH连接管理

#### 4.3.1 SSH连接池实现
```go
package host

import (
    "fmt"
    "net"
    "time"
    
    "golang.org/x/crypto/ssh"
)

type SSHClient struct {
    Host     string
    Port     int
    Username string
    AuthType string
    Password string
    KeyPath  string
    client   *ssh.Client
}

func (s *SSHClient) Connect() error {
    config := &ssh.ClientConfig{
        User:            s.Username,
        HostKeyCallback: ssh.InsecureIgnoreHostKey(),
        Timeout:         30 * time.Second,
    }
    
    // 根据认证类型设置认证方法
    switch s.AuthType {
    case "password":
        config.Auth = []ssh.AuthMethod{
            ssh.Password(s.Password),
        }
    case "key":
        key, err := s.loadPrivateKey()
        if err != nil {
            return err
        }
        config.Auth = []ssh.AuthMethod{
            ssh.PublicKeys(key),
        }
    }
    
    addr := fmt.Sprintf("%s:%d", s.Host, s.Port)
    client, err := ssh.Dial("tcp", addr, config)
    if err != nil {
        return err
    }
    
    s.client = client
    return nil
}

func (s *SSHClient) ExecuteCommand(command string) (string, error) {
    if s.client == nil {
        return "", fmt.Errorf("not connected")
    }
    
    session, err := s.client.NewSession()
    if err != nil {
        return "", err
    }
    defer session.Close()
    
    output, err := session.CombinedOutput(command)
    return string(output), err
}

func (s *SSHClient) Close() error {
    if s.client != nil {
        return s.client.Close()
    }
    return nil
}
```

## 5. 构建和部署

### 5.1 构建脚本

#### 5.1.1 基本构建 (scripts/build.sh)
```bash
#!/bin/bash

# 设置变量
APP_NAME="linux-security-checker"
VERSION=$(git describe --tags --always --dirty)
BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
GO_VERSION=$(go version | awk '{print $3}')

# 构建标志
LDFLAGS="-X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GoVersion=${GO_VERSION}"

echo "Building ${APP_NAME} version ${VERSION}..."

# 清理之前的构建
rm -rf dist/
mkdir -p dist/

# 构建Windows版本
echo "Building for Windows..."
GOOS=windows GOARCH=amd64 go build -ldflags="${LDFLAGS}" -o dist/${APP_NAME}-windows-amd64.exe cmd/main.go

# 构建Linux版本 (用于开发测试)
echo "Building for Linux..."
GOOS=linux GOARCH=amd64 go build -ldflags="${LDFLAGS}" -o dist/${APP_NAME}-linux-amd64 cmd/main.go

echo "Build completed!"
```

#### 5.1.2 Fyne打包脚本 (scripts/package.sh)
```bash
#!/bin/bash

APP_NAME="linux-security-checker"
ICON_PATH="assets/icons/app.png"

echo "Packaging with Fyne..."

# 确保fyne工具已安装
if ! command -v fyne &> /dev/null; then
    echo "Installing fyne tool..."
    go install fyne.io/tools/cmd/fyne@latest
fi

# 打包Windows版本
echo "Packaging for Windows..."
fyne package -os windows -icon ${ICON_PATH} -name ${APP_NAME}

# 移动到dist目录
mkdir -p dist/
mv ${APP_NAME}.exe dist/

echo "Packaging completed!"
```

### 5.2 Makefile配置
```makefile
.PHONY: build package test clean install deps

# 变量定义
APP_NAME := linux-security-checker
VERSION := $(shell git describe --tags --always --dirty)
BUILD_DIR := dist

# 默认目标
all: deps build

# 安装依赖
deps:
	go mod download
	go install fyne.io/tools/cmd/fyne@latest

# 构建
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@GOOS=windows GOARCH=amd64 go build -o $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe cmd/main.go
	@echo "Build completed!"

# Fyne打包
package: build
	@echo "Packaging with Fyne..."
	@fyne package -os windows -icon assets/icons/app.png -name $(APP_NAME)
	@mv $(APP_NAME).exe $(BUILD_DIR)/
	@echo "Packaging completed!"

# 运行测试
test:
	go test -v ./...

# 清理构建文件
clean:
	rm -rf $(BUILD_DIR)
	rm -f $(APP_NAME).exe

# 安装到本地
install:
	go install cmd/main.go

# 开发模式运行
dev:
	go run cmd/main.go

# 格式化代码
fmt:
	go fmt ./...

# 代码检查
lint:
	golangci-lint run

# 生成资源文件
bundle:
	fyne bundle -o internal/gui/resources.go assets/icons/app.png
```

## 6. 开发规范

### 6.1 代码规范
- 使用 `gofmt` 格式化代码
- 遵循 Go 官方命名规范
- 每个包都要有包级别的文档注释
- 公开的函数和类型必须有文档注释
- 错误处理要明确，不能忽略错误

### 6.2 Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 6.3 目录命名规范
- 包名使用小写字母
- 目录名使用小写字母和下划线
- 文件名使用小写字母和下划线
- 常量使用大写字母和下划线

## 7. 调试和测试

### 7.1 开发环境运行
```bash
# 直接运行
go run cmd/main.go

# 或使用make
make dev
```

### 7.2 单元测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/core/host

# 运行测试并显示覆盖率
go test -cover ./...
```

### 7.3 调试技巧
- 使用 `fmt.Printf` 或 `log.Printf` 进行调试输出
- 使用 VS Code 的 Go 扩展进行断点调试
- 使用 `go build -race` 检测竞态条件

## 8. 常见问题解决

### 8.1 Fyne相关问题
```bash
# 如果fyne命令找不到
export PATH=$PATH:$(go env GOPATH)/bin

# 如果GUI不显示
export DISPLAY=:0  # Linux下
```

### 8.2 交叉编译问题
```bash
# 如果Windows交叉编译失败，安装mingw-w64
# Ubuntu/Debian:
sudo apt-get install gcc-mingw-w64

# 设置CGO环境
export CGO_ENABLED=1
export CC=x86_64-w64-mingw32-gcc
```

### 8.3 依赖问题
```bash
# 清理模块缓存
go clean -modcache

# 重新下载依赖
go mod download

# 整理依赖
go mod tidy
```

## 9. 核心模块详细实现

### 9.1 主机管理模块

#### 9.1.1 主机模型定义
```go
package models

import (
    "time"
    "database/sql/driver"
    "encoding/json"
)

type Host struct {
    ID          int       `json:"id" db:"id"`
    Name        string    `json:"name" db:"name"`
    IPAddress   string    `json:"ip_address" db:"ip_address"`
    Port        int       `json:"port" db:"port"`
    Username    string    `json:"username" db:"username"`
    AuthType    string    `json:"auth_type" db:"auth_type"` // "password" or "key"
    Password    string    `json:"password,omitempty" db:"password"`
    PrivateKey  string    `json:"private_key,omitempty" db:"private_key"`
    GroupID     *int      `json:"group_id" db:"group_id"`
    Status      string    `json:"status" db:"status"` // "online", "offline", "unknown"
    LastCheck   *time.Time `json:"last_check" db:"last_check"`
    CreatedAt   time.Time `json:"created_at" db:"created_at"`
    UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

type HostGroup struct {
    ID          int       `json:"id" db:"id"`
    Name        string    `json:"name" db:"name"`
    Description string    `json:"description" db:"description"`
    Hosts       []Host    `json:"hosts,omitempty"`
    CreatedAt   time.Time `json:"created_at" db:"created_at"`
}
```

#### 9.1.2 主机管理器实现
```go
package host

import (
    "context"
    "database/sql"
    "fmt"
    "sync"
    "time"

    "github.com/yourname/linux-security-checker/internal/db/models"
)

type Manager struct {
    db          *sql.DB
    sshClients  map[int]*SSHClient
    clientMutex sync.RWMutex
}

func NewManager(db *sql.DB) *Manager {
    return &Manager{
        db:         db,
        sshClients: make(map[int]*SSHClient),
    }
}

func (m *Manager) AddHost(host *models.Host) error {
    query := `
        INSERT INTO hosts (name, ip_address, port, username, auth_type, password, private_key, group_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `
    result, err := m.db.Exec(query, host.Name, host.IPAddress, host.Port,
        host.Username, host.AuthType, host.Password, host.PrivateKey, host.GroupID)
    if err != nil {
        return err
    }

    id, err := result.LastInsertId()
    if err != nil {
        return err
    }

    host.ID = int(id)
    return nil
}

func (m *Manager) GetHost(id int) (*models.Host, error) {
    query := `SELECT * FROM hosts WHERE id = ?`
    row := m.db.QueryRow(query, id)

    var host models.Host
    err := row.Scan(&host.ID, &host.Name, &host.IPAddress, &host.Port,
        &host.Username, &host.AuthType, &host.Password, &host.PrivateKey,
        &host.GroupID, &host.Status, &host.LastCheck, &host.CreatedAt, &host.UpdatedAt)
    if err != nil {
        return nil, err
    }

    return &host, nil
}

func (m *Manager) TestConnection(hostID int) error {
    host, err := m.GetHost(hostID)
    if err != nil {
        return err
    }

    client := &SSHClient{
        Host:     host.IPAddress,
        Port:     host.Port,
        Username: host.Username,
        AuthType: host.AuthType,
        Password: host.Password,
        KeyPath:  host.PrivateKey,
    }

    if err := client.Connect(); err != nil {
        return err
    }
    defer client.Close()

    // 执行简单命令测试连接
    _, err = client.ExecuteCommand("echo 'connection test'")
    return err
}

func (m *Manager) GetSSHClient(hostID int) (*SSHClient, error) {
    m.clientMutex.RLock()
    client, exists := m.sshClients[hostID]
    m.clientMutex.RUnlock()

    if exists && client.IsConnected() {
        return client, nil
    }

    // 创建新连接
    host, err := m.GetHost(hostID)
    if err != nil {
        return nil, err
    }

    client = &SSHClient{
        Host:     host.IPAddress,
        Port:     host.Port,
        Username: host.Username,
        AuthType: host.AuthType,
        Password: host.Password,
        KeyPath:  host.PrivateKey,
    }

    if err := client.Connect(); err != nil {
        return nil, err
    }

    m.clientMutex.Lock()
    m.sshClients[hostID] = client
    m.clientMutex.Unlock()

    return client, nil
}
```

### 9.2 规则引擎模块

#### 9.2.1 规则模型定义
```go
package models

type Rule struct {
    ID             int       `json:"id" db:"id"`
    Name           string    `json:"name" db:"name"`
    Category       string    `json:"category" db:"category"`
    Description    string    `json:"description" db:"description"`
    Command        string    `json:"command" db:"command"`
    ExpectedResult string    `json:"expected_result" db:"expected_result"`
    CheckType      string    `json:"check_type" db:"check_type"` // "exact", "contains", "regex", "exit_code"
    Severity       string    `json:"severity" db:"severity"` // "low", "medium", "high", "critical"
    Enabled        bool      `json:"enabled" db:"enabled"`
    Tags           []string  `json:"tags" db:"tags"`
    CreatedAt      time.Time `json:"created_at" db:"created_at"`
}

type RuleCategory struct {
    Name        string `json:"name"`
    DisplayName string `json:"display_name"`
    Description string `json:"description"`
    Rules       []Rule `json:"rules"`
}
```

#### 9.2.2 规则引擎实现
```go
package rule

import (
    "encoding/json"
    "fmt"
    "regexp"
    "strconv"
    "strings"

    "github.com/yourname/linux-security-checker/internal/db/models"
)

type Engine struct {
    rules map[string]*models.Rule
}

func NewEngine() *Engine {
    return &Engine{
        rules: make(map[string]*models.Rule),
    }
}

func (e *Engine) LoadRules(rules []models.Rule) {
    for _, rule := range rules {
        e.rules[fmt.Sprintf("%d", rule.ID)] = &rule
    }
}

func (e *Engine) ExecuteRule(rule *models.Rule, output string, exitCode int) (*RuleResult, error) {
    result := &RuleResult{
        RuleID:   rule.ID,
        RuleName: rule.Name,
        Status:   "unknown",
        Output:   output,
        ExitCode: exitCode,
    }

    switch rule.CheckType {
    case "exit_code":
        expectedCode, err := strconv.Atoi(rule.ExpectedResult)
        if err != nil {
            return nil, fmt.Errorf("invalid expected exit code: %s", rule.ExpectedResult)
        }
        if exitCode == expectedCode {
            result.Status = "pass"
        } else {
            result.Status = "fail"
            result.Message = fmt.Sprintf("Expected exit code %d, got %d", expectedCode, exitCode)
        }

    case "exact":
        if strings.TrimSpace(output) == strings.TrimSpace(rule.ExpectedResult) {
            result.Status = "pass"
        } else {
            result.Status = "fail"
            result.Message = "Output does not match expected result exactly"
        }

    case "contains":
        if strings.Contains(output, rule.ExpectedResult) {
            result.Status = "pass"
        } else {
            result.Status = "fail"
            result.Message = fmt.Sprintf("Output does not contain expected text: %s", rule.ExpectedResult)
        }

    case "regex":
        matched, err := regexp.MatchString(rule.ExpectedResult, output)
        if err != nil {
            return nil, fmt.Errorf("invalid regex pattern: %s", rule.ExpectedResult)
        }
        if matched {
            result.Status = "pass"
        } else {
            result.Status = "fail"
            result.Message = "Output does not match expected regex pattern"
        }

    default:
        return nil, fmt.Errorf("unsupported check type: %s", rule.CheckType)
    }

    return result, nil
}

type RuleResult struct {
    RuleID   int    `json:"rule_id"`
    RuleName string `json:"rule_name"`
    Status   string `json:"status"` // "pass", "fail", "error"
    Output   string `json:"output"`
    ExitCode int    `json:"exit_code"`
    Message  string `json:"message"`
}
```

### 9.3 核查执行引擎

#### 9.3.1 任务调度器
```go
package checker

import (
    "context"
    "fmt"
    "sync"
    "time"

    "github.com/yourname/linux-security-checker/internal/core/host"
    "github.com/yourname/linux-security-checker/internal/core/rule"
    "github.com/yourname/linux-security-checker/internal/db/models"
)

type Scheduler struct {
    hostManager *host.Manager
    ruleEngine  *rule.Engine
    maxWorkers  int
    taskQueue   chan *Task
    results     chan *TaskResult
    wg          sync.WaitGroup
}

type Task struct {
    ID      string
    HostID  int
    Rules   []models.Rule
    Context context.Context
}

type TaskResult struct {
    TaskID    string
    HostID    int
    Results   []rule.RuleResult
    Error     error
    StartTime time.Time
    EndTime   time.Time
}

func NewScheduler(hostManager *host.Manager, ruleEngine *rule.Engine, maxWorkers int) *Scheduler {
    return &Scheduler{
        hostManager: hostManager,
        ruleEngine:  ruleEngine,
        maxWorkers:  maxWorkers,
        taskQueue:   make(chan *Task, 100),
        results:     make(chan *TaskResult, 100),
    }
}

func (s *Scheduler) Start() {
    for i := 0; i < s.maxWorkers; i++ {
        s.wg.Add(1)
        go s.worker()
    }
}

func (s *Scheduler) Stop() {
    close(s.taskQueue)
    s.wg.Wait()
    close(s.results)
}

func (s *Scheduler) SubmitTask(task *Task) {
    select {
    case s.taskQueue <- task:
    case <-task.Context.Done():
        // 任务已取消
    }
}

func (s *Scheduler) GetResults() <-chan *TaskResult {
    return s.results
}

func (s *Scheduler) worker() {
    defer s.wg.Done()

    for task := range s.taskQueue {
        result := s.executeTask(task)

        select {
        case s.results <- result:
        case <-task.Context.Done():
            // 上下文已取消
        }
    }
}

func (s *Scheduler) executeTask(task *Task) *TaskResult {
    result := &TaskResult{
        TaskID:    task.ID,
        HostID:    task.HostID,
        StartTime: time.Now(),
    }

    // 获取SSH客户端
    client, err := s.hostManager.GetSSHClient(task.HostID)
    if err != nil {
        result.Error = fmt.Errorf("failed to get SSH client: %w", err)
        result.EndTime = time.Now()
        return result
    }

    // 执行规则检查
    for _, rule := range task.Rules {
        select {
        case <-task.Context.Done():
            result.Error = fmt.Errorf("task cancelled")
            result.EndTime = time.Now()
            return result
        default:
        }

        // 执行命令
        output, err := client.ExecuteCommand(rule.Command)
        exitCode := 0
        if err != nil {
            // 尝试从错误中提取退出码
            if exitError, ok := err.(*ssh.ExitError); ok {
                exitCode = exitError.ExitStatus()
            } else {
                // 连接或其他错误
                ruleResult := rule.RuleResult{
                    RuleID:   rule.ID,
                    RuleName: rule.Name,
                    Status:   "error",
                    Message:  err.Error(),
                }
                result.Results = append(result.Results, ruleResult)
                continue
            }
        }

        // 评估规则结果
        ruleResult, err := s.ruleEngine.ExecuteRule(&rule, output, exitCode)
        if err != nil {
            ruleResult = &rule.RuleResult{
                RuleID:   rule.ID,
                RuleName: rule.Name,
                Status:   "error",
                Message:  err.Error(),
            }
        }

        result.Results = append(result.Results, *ruleResult)
    }

    result.EndTime = time.Now()
    return result
}
```

### 9.4 GUI页面实现示例

#### 9.4.1 主机管理页面
```go
package pages

import (
    "fmt"
    "strconv"

    "fyne.io/fyne/v2"
    "fyne.io/fyne/v2/container"
    "fyne.io/fyne/v2/dialog"
    "fyne.io/fyne/v2/widget"

    "github.com/yourname/linux-security-checker/internal/core/host"
    "github.com/yourname/linux-security-checker/internal/db/models"
)

type HostsPage struct {
    manager    *host.Manager
    window     fyne.Window
    hostList   *widget.List
    hosts      []models.Host
    content    *container.Split
}

func NewHostsPage(manager *host.Manager, window fyne.Window) *HostsPage {
    page := &HostsPage{
        manager: manager,
        window:  window,
        hosts:   []models.Host{},
    }

    page.createUI()
    page.refreshHosts()

    return page
}

func (p *HostsPage) createUI() {
    // 创建主机列表
    p.hostList = widget.NewList(
        func() int {
            return len(p.hosts)
        },
        func() fyne.CanvasObject {
            return container.NewHBox(
                widget.NewLabel("Template"),
                widget.NewLabel("Template"),
                widget.NewLabel("Template"),
            )
        },
        func(id widget.ListItemID, obj fyne.CanvasObject) {
            if id >= len(p.hosts) {
                return
            }

            host := p.hosts[id]
            container := obj.(*container.HBox)

            nameLabel := container.Objects[0].(*widget.Label)
            ipLabel := container.Objects[1].(*widget.Label)
            statusLabel := container.Objects[2].(*widget.Label)

            nameLabel.SetText(host.Name)
            ipLabel.SetText(fmt.Sprintf("%s:%d", host.IPAddress, host.Port))
            statusLabel.SetText(host.Status)
        },
    )

    // 创建工具栏
    toolbar := container.NewHBox(
        widget.NewButton("添加主机", p.showAddHostDialog),
        widget.NewButton("编辑主机", p.editSelectedHost),
        widget.NewButton("删除主机", p.deleteSelectedHost),
        widget.NewButton("测试连接", p.testSelectedHost),
        widget.NewButton("刷新", p.refreshHosts),
    )

    // 创建主机详情面板
    detailPanel := widget.NewCard("主机详情", "", widget.NewLabel("选择一个主机查看详情"))

    // 创建布局
    leftPanel := container.NewBorder(toolbar, nil, nil, nil, p.hostList)
    p.content = container.NewHSplit(leftPanel, detailPanel)
    p.content.SetOffset(0.7)
}

func (p *HostsPage) showAddHostDialog() {
    nameEntry := widget.NewEntry()
    nameEntry.SetPlaceHolder("主机名称")

    ipEntry := widget.NewEntry()
    ipEntry.SetPlaceHolder("IP地址")

    portEntry := widget.NewEntry()
    portEntry.SetText("22")
    portEntry.SetPlaceHolder("SSH端口")

    usernameEntry := widget.NewEntry()
    usernameEntry.SetPlaceHolder("用户名")

    authTypeSelect := widget.NewSelect([]string{"password", "key"}, nil)
    authTypeSelect.SetSelected("password")

    passwordEntry := widget.NewPasswordEntry()
    passwordEntry.SetPlaceHolder("密码")

    keyEntry := widget.NewMultiLineEntry()
    keyEntry.SetPlaceHolder("私钥内容")
    keyEntry.Hide()

    authTypeSelect.OnChanged = func(value string) {
        if value == "password" {
            passwordEntry.Show()
            keyEntry.Hide()
        } else {
            passwordEntry.Hide()
            keyEntry.Show()
        }
    }

    form := container.NewVBox(
        widget.NewLabel("添加新主机"),
        nameEntry,
        ipEntry,
        portEntry,
        usernameEntry,
        authTypeSelect,
        passwordEntry,
        keyEntry,
    )

    dialog.ShowCustomConfirm("添加主机", "确定", "取消", form, func(confirmed bool) {
        if !confirmed {
            return
        }

        port, err := strconv.Atoi(portEntry.Text)
        if err != nil {
            dialog.ShowError(fmt.Errorf("无效的端口号: %s", portEntry.Text), p.window)
            return
        }

        host := &models.Host{
            Name:      nameEntry.Text,
            IPAddress: ipEntry.Text,
            Port:      port,
            Username:  usernameEntry.Text,
            AuthType:  authTypeSelect.Selected,
        }

        if authTypeSelect.Selected == "password" {
            host.Password = passwordEntry.Text
        } else {
            host.PrivateKey = keyEntry.Text
        }

        if err := p.manager.AddHost(host); err != nil {
            dialog.ShowError(err, p.window)
            return
        }

        p.refreshHosts()
    }, p.window)
}

func (p *HostsPage) refreshHosts() {
    hosts, err := p.manager.GetAllHosts()
    if err != nil {
        dialog.ShowError(err, p.window)
        return
    }

    p.hosts = hosts
    p.hostList.Refresh()
}

func (p *HostsPage) GetContent() fyne.CanvasObject {
    return p.content
}
```

这个开发文档为项目提供了完整的技术实现指导，包含了环境搭建、项目结构、核心技术实现、详细的模块实现示例和构建部署等所有必要信息。开发人员可以根据这个文档快速上手项目开发。
