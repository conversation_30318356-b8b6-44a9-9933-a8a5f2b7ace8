# Linux等保基线核查工具开发需求文档

## 1. 项目概述

### 1.1 项目名称
Linux等保基线核查工具 (Linux Security Baseline Checker)

### 1.2 项目背景
为满足等级保护2.0标准要求，开发一款专业的Linux系统安全基线核查工具，帮助用户快速检测和评估Linux系统的安全配置合规性。

### 1.3 项目目标
- 提供直观易用的图形化界面
- 支持单主机和多主机批量核查
- 基于等保2.0标准的安全基线规则
- 生成详细的核查报告和整改建议
- 支持多种格式的结果导出

## 2. 功能需求

### 2.1 主机管理
#### 2.1.1 单主机管理
- **添加主机**: 支持IP地址、主机名、SSH端口、认证方式配置
- **编辑主机**: 修改主机连接信息和标签
- **删除主机**: 删除不需要的主机配置
- **连接测试**: 验证SSH连接是否正常
- **主机分组**: 支持按业务、环境等维度分组

#### 2.1.2 多主机组管理
- **创建主机组**: 将多个主机组织成逻辑组
- **组成员管理**: 添加/移除组内主机
- **组配置**: 设置组级别的核查参数
- **批量操作**: 支持批量添加、删除、修改主机

### 2.2 核查规则管理
#### 2.2.1 基线规则
- **系统安全**: 用户账户、密码策略、权限配置
- **网络安全**: 防火墙、端口服务、网络配置
- **日志审计**: 日志配置、审计策略
- **文件系统**: 文件权限、关键目录保护
- **应用安全**: 系统服务、软件版本检查

#### 2.2.2 规则配置
- **规则启用/禁用**: 灵活选择检查项目
- **自定义规则**: 支持用户自定义检查规则
- **规则分类**: 按等保要求分类管理
- **规则更新**: 支持规则库在线更新

### 2.3 核查执行
#### 2.3.1 核查任务
- **单主机核查**: 对单个主机执行安全基线检查
- **多主机核查**: 并发执行多主机检查任务
- **定时核查**: 支持定时自动执行核查任务
- **进度监控**: 实时显示核查进度和状态

#### 2.3.2 核查引擎
- **SSH连接**: 通过SSH远程执行检查命令
- **并发控制**: 控制并发连接数，避免资源过载
- **错误处理**: 处理网络异常、认证失败等情况
- **结果收集**: 收集和解析检查结果

### 2.4 结果管理
#### 2.4.1 结果查看
- **核查报告**: 展示详细的核查结果
- **风险等级**: 按高中低风险分类显示
- **合规统计**: 统计合规率和问题分布
- **历史记录**: 查看历史核查记录

#### 2.4.2 结果导出
- **PDF报告**: 生成专业的PDF格式报告
- **Excel表格**: 导出详细的Excel数据表
- **HTML报告**: 生成可在浏览器查看的报告
- **JSON数据**: 导出结构化的JSON数据

## 3. 技术需求

### 3.1 开发语言和框架
- **主语言**: Go 1.21+
- **GUI框架**: Fyne v2.4+
- **数据库**: SQLite 3
- **SSH库**: golang.org/x/crypto/ssh

### 3.2 系统要求
- **目标平台**: Windows 10/11 (64位)
- **运行环境**: 无需额外依赖，单文件执行
- **内存要求**: 最小256MB，推荐512MB
- **存储空间**: 最小100MB

### 3.3 性能要求
- **并发连接**: 支持最多50个并发SSH连接
- **响应时间**: 界面操作响应时间<500ms
- **核查速度**: 单主机核查时间<5分钟
- **资源占用**: CPU占用率<30%，内存占用<200MB

## 4. 界面设计

### 4.1 整体布局
- **侧边栏导航**: 左侧固定导航栏，包含主要功能模块
- **主内容区**: 右侧动态内容区域
- **状态栏**: 底部状态信息显示
- **工具栏**: 顶部快捷操作按钮

### 4.2 主要页面
#### 4.2.1 主机管理页面
- 主机列表表格
- 添加/编辑主机对话框
- 主机组管理面板
- 连接状态指示器

#### 4.2.2 核查规则页面
- 规则分类树形结构
- 规则详情展示区
- 规则启用/禁用开关
- 自定义规则编辑器

#### 4.2.3 核查任务页面
- 任务创建向导
- 任务执行进度条
- 实时日志输出
- 任务历史记录

#### 4.2.4 核查结果页面
- 结果概览仪表板
- 详细结果表格
- 风险分析图表
- 导出功能按钮

#### 4.2.5 系统设置页面
- 全局配置选项
- SSH连接参数
- 导出格式设置
- 关于信息

## 5. 技术架构

### 5.1 项目结构
```
linux-security-checker/
├── cmd/
│   └── main.go                 # 主程序入口
├── internal/
│   ├── gui/                    # GUI相关代码
│   │   ├── app.go             # 应用主框架
│   │   ├── pages/             # 各页面实现
│   │   └── components/        # 通用组件
│   ├── core/                  # 核心业务逻辑
│   │   ├── host/              # 主机管理
│   │   ├── rule/              # 规则管理
│   │   ├── checker/           # 核查引擎
│   │   └── export/            # 导出功能
│   ├── db/                    # 数据库操作
│   │   ├── models/            # 数据模型
│   │   └── migrations/        # 数据库迁移
│   └── config/                # 配置管理
├── configs/                   # 配置文件
├── rules/                     # 等保基线规则
├── assets/                    # 静态资源
├── docs/                      # 文档
└── scripts/                   # 构建脚本
```

### 5.2 核心模块
#### 5.2.1 GUI模块
- 基于Fyne框架的图形界面
- 响应式布局设计
- 主题和样式管理

#### 5.2.2 主机管理模块
- 主机信息CRUD操作
- SSH连接池管理
- 主机状态监控

#### 5.2.3 规则引擎模块
- 规则解析和执行
- 结果评估和分析
- 自定义规则支持

#### 5.2.4 核查引擎模块
- SSH远程命令执行
- 并发任务调度
- 错误处理和重试

#### 5.2.5 数据存储模块
- SQLite数据库操作
- 数据模型定义
- 数据迁移管理

## 6. 开发计划

### 6.1 开发阶段
#### 第一阶段：基础框架 (2周)
- [ ] 项目初始化和依赖管理
- [ ] Fyne GUI基础框架搭建
- [ ] SQLite数据库设计和初始化
- [ ] 基础配置管理

#### 第二阶段：主机管理 (2周)
- [ ] 主机信息CRUD功能
- [ ] SSH连接测试功能
- [ ] 主机分组管理
- [ ] 主机状态监控

#### 第三阶段：规则管理 (2周)
- [ ] 等保基线规则定义
- [ ] 规则配置界面
- [ ] 自定义规则支持
- [ ] 规则验证功能

#### 第四阶段：核查引擎 (3周)
- [ ] SSH远程执行引擎
- [ ] 单主机核查功能
- [ ] 多主机并发核查
- [ ] 进度监控和日志

#### 第五阶段：结果管理 (2周)
- [ ] 核查结果展示
- [ ] 风险分析和统计
- [ ] 多格式导出功能
- [ ] 历史记录管理

#### 第六阶段：优化测试 (1周)
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] Windows交叉编译
- [ ] 功能测试和修复

### 6.2 里程碑
- **M1**: 基础框架完成 (第2周)
- **M2**: 主机管理功能完成 (第4周)
- **M3**: 核查功能完成 (第9周)
- **M4**: 完整功能交付 (第12周)

## 7. 风险评估

### 7.1 技术风险
- **SSH连接稳定性**: 网络环境复杂可能导致连接不稳定
- **并发性能**: 大量主机并发核查可能影响性能
- **跨平台兼容性**: Windows平台的兼容性问题

### 7.2 业务风险
- **规则准确性**: 等保基线规则的准确性和完整性
- **用户体验**: GUI界面的易用性和响应速度
- **数据安全**: 主机认证信息的安全存储

### 7.3 风险缓解
- 实现连接重试和错误恢复机制
- 优化并发控制和资源管理
- 充分测试跨平台兼容性
- 建立规则验证和更新机制
- 加强数据加密和访问控制

## 8. 交付物

### 8.1 软件交付
- Windows可执行文件 (.exe)
- 用户使用手册
- 安装部署指南

### 8.2 文档交付
- 详细设计文档
- API接口文档
- 测试报告
- 运维手册

### 8.3 源码交付
- 完整源代码
- 构建脚本
- 单元测试代码
- 部署配置文件
